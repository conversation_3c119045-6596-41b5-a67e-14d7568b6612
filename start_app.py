#!/usr/bin/env python3
"""
启动应用程序的简化脚本
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("正在导入应用程序...")
    from app import app, init_video_queues, video_queues, logger
    
    print("正在初始化视频队列...")
    init_video_queues()
    
    print("视频模型队列初始化完成")
    for model_key, queue_info in video_queues.items():
        print(f"- {model_key}: {queue_info['model_name']}")
    
    print("正在启动Flask应用...")
    app.run(host='0.0.0.0', port=7799, debug=False)
    
except Exception as e:
    print(f"启动失败: {e}")
    import traceback
    traceback.print_exc()
