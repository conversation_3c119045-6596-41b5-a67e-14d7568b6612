#!/usr/bin/env python3
"""
测试统计数据修复
"""

import requests
import json

def test_statistics_api():
    """测试统计API"""
    base_url = 'http://localhost:5000'
    
    # 创建会话
    session = requests.Session()
    
    print("测试统计数据API...")
    
    # 测试统计API
    response = session.get(f'{base_url}/admin/statistics')
    print(f"统计API状态码: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print("返回的数据结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            if data.get('success'):
                stats = data['statistics']
                print("\n解析的统计数据:")
                print(f"总用户数: {stats.get('total_users', 'N/A')}")
                
                system_stats = stats.get('system_stats', {})
                print(f"总生成次数: {system_stats.get('total_generations', 'N/A')}")
                print(f"总消耗积分: {system_stats.get('total_points_consumed', 'N/A')}")
                print(f"总发放积分: {system_stats.get('total_points_issued', 'N/A')}")
                print(f"总交易数: {system_stats.get('total_transactions', 'N/A')}")
            else:
                print(f"API返回错误: {data.get('message')}")
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            print(f"响应内容: {response.text}")
    else:
        print(f"请求失败: {response.text}")

if __name__ == '__main__':
    test_statistics_api()
