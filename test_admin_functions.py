#!/usr/bin/env python3
"""
测试管理员用户管理功能的脚本
"""

import requests
import json

# 测试配置
BASE_URL = "http://localhost:7799"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"
import time
TEST_USERNAME = f"testuser_{int(time.time())}"  # 使用时间戳确保唯一性
TEST_PASSWORD = "testpass123"

def login_admin():
    """管理员登录"""
    session = requests.Session()
    
    # 登录管理员账户
    login_data = {
        "username": ADMIN_USERNAME,
        "password": ADMIN_PASSWORD
    }
    
    response = session.post(f"{BASE_URL}/login", json=login_data)
    result = response.json()
    
    if result.get('success'):
        print(f"✓ 管理员登录成功: {result.get('message')}")
        return session
    else:
        print(f"✗ 管理员登录失败: {result.get('message')}")
        return None

def create_test_user():
    """创建测试用户（使用新的session）"""
    # 使用新的session来创建用户，避免影响管理员session
    temp_session = requests.Session()

    register_data = {
        "username": TEST_USERNAME,
        "password": TEST_PASSWORD,
        "email": "<EMAIL>"
    }

    response = temp_session.post(f"{BASE_URL}/register", json=register_data)
    result = response.json()

    if result.get('success'):
        print(f"✓ 测试用户创建成功: {result.get('message')}")
        return True
    else:
        print(f"✗ 测试用户创建失败: {result.get('message')}")
        return False

def test_get_users(session):
    """测试获取用户列表"""
    # 先测试用户信息接口
    user_info_response = session.get(f"{BASE_URL}/user_info")
    user_info_result = user_info_response.json()
    print(f"当前用户信息: {user_info_result}")

    response = session.get(f"{BASE_URL}/admin/users")
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text[:200]}...")

    try:
        result = response.json()
    except:
        print(f"✗ 无法解析JSON响应")
        return False

    if result.get('success'):
        users = result.get('users', [])
        print(f"✓ 获取用户列表成功，共 {len(users)} 个用户")

        # 查找测试用户
        test_user = next((u for u in users if u['username'] == TEST_USERNAME), None)
        if test_user:
            print(f"  - 找到测试用户: {test_user['username']}")
            return True
        else:
            print(f"  - 未找到测试用户: {TEST_USERNAME}")
            return False
    else:
        print(f"✗ 获取用户列表失败: {result.get('message')}")
        return False

def test_reset_password(session):
    """测试重置用户密码"""
    new_password = "newpassword123"

    reset_data = {
        "username": TEST_USERNAME,
        "new_password": new_password
    }

    response = session.post(f"{BASE_URL}/admin/reset_user_password", json=reset_data)
    print(f"重置密码响应状态码: {response.status_code}")
    print(f"重置密码响应内容: {response.text}")

    try:
        result = response.json()
    except:
        print(f"✗ 无法解析重置密码的JSON响应")
        return False

    if result.get('success'):
        print(f"✓ 重置密码成功: {result.get('message')}")

        # 测试新密码是否有效
        test_session = requests.Session()
        login_data = {
            "username": TEST_USERNAME,
            "password": new_password
        }

        login_response = test_session.post(f"{BASE_URL}/login", json=login_data)
        login_result = login_response.json()

        if login_result.get('success'):
            print(f"  ✓ 新密码验证成功")
            return True
        else:
            print(f"  ✗ 新密码验证失败: {login_result.get('message')}")
            return False
    else:
        print(f"✗ 重置密码失败: {result.get('message')}")
        return False

def test_delete_user(session):
    """测试删除用户"""
    delete_data = {
        "username": TEST_USERNAME
    }
    
    response = session.post(f"{BASE_URL}/admin/delete_user", json=delete_data)
    result = response.json()
    
    if result.get('success'):
        print(f"✓ 删除用户成功: {result.get('message')}")
        
        # 验证用户是否真的被删除
        users_response = session.get(f"{BASE_URL}/admin/users")
        users_result = users_response.json()
        
        if users_result.get('success'):
            users = users_result.get('users', [])
            test_user = next((u for u in users if u['username'] == TEST_USERNAME), None)
            
            if test_user is None:
                print(f"  ✓ 用户已从列表中删除")
                return True
            else:
                print(f"  ✗ 用户仍在列表中")
                return False
        else:
            print(f"  ✗ 无法验证删除结果")
            return False
    else:
        print(f"✗ 删除用户失败: {result.get('message')}")
        return False

def main():
    """主测试函数"""
    print("开始测试管理员用户管理功能...")
    print("=" * 50)
    
    # 1. 管理员登录
    session = login_admin()
    if not session:
        print("无法继续测试，管理员登录失败")
        return
    
    # 2. 创建测试用户
    if not create_test_user():
        print("无法继续测试，测试用户创建失败")
        return
    
    # 3. 测试获取用户列表
    if not test_get_users(session):
        print("获取用户列表测试失败")
        return
    
    # 4. 测试重置密码
    if not test_reset_password(session):
        print("重置密码测试失败")
        return
    
    # 5. 测试删除用户
    if not test_delete_user(session):
        print("删除用户测试失败")
        return
    
    print("=" * 50)
    print("✓ 所有测试通过！管理员用户管理功能正常工作。")

if __name__ == "__main__":
    main()
