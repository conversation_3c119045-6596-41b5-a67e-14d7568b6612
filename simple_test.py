#!/usr/bin/env python3
"""
简单测试新添加的用户管理功能
"""

from auth import UserManager
import json

def test_user_management():
    """测试用户管理功能"""
    print("测试用户管理功能...")
    
    # 初始化用户管理器
    user_manager = UserManager()
    
    # 创建测试用户
    test_username = "test_simple_user"
    test_password = "testpass123"
    
    print(f"1. 创建测试用户: {test_username}")
    success, message = user_manager.register_user(test_username, test_password, "<EMAIL>")
    print(f"   结果: {message}")
    
    if not success:
        print("   用户创建失败，跳过后续测试")
        return
    
    # 测试重置密码
    print(f"2. 测试重置密码")
    new_password = "newpassword123"
    success, message = user_manager.reset_user_password(test_username, new_password, "admin")
    print(f"   结果: {message}")
    
    if success:
        # 验证新密码
        print(f"3. 验证新密码")
        success, message = user_manager.login_user(test_username, new_password)
        print(f"   结果: {message}")
    
    # 测试删除用户
    print(f"4. 测试删除用户")
    success, message = user_manager.delete_user(test_username, "admin")
    print(f"   结果: {message}")
    
    if success:
        # 验证用户已删除
        print(f"5. 验证用户已删除")
        user = user_manager.get_user(test_username)
        if user is None:
            print("   ✓ 用户已成功删除")
        else:
            print("   ✗ 用户仍然存在")
    
    print("测试完成！")

if __name__ == "__main__":
    test_user_management()
